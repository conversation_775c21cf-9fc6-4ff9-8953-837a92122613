import 'package:equatable/equatable.dart';
import 'package:room_eight/core/api_config/endpoints/socket_key.dart';
import 'package:room_eight/core/socket/socket_service.dart';
import 'package:room_eight/core/utils/app_exports.dart';
import 'package:room_eight/repository/chat_repository/chat_repository.dart';
import 'package:room_eight/views/chat/model/chat_list_model.dart';
import 'package:room_eight/views/chat/model/chat_message_list_model.dart';
import 'package:room_eight/views/chat/model/search_user_model.dart';
import 'package:stream_transform/stream_transform.dart';

part 'chat_event.dart';
part 'chat_state.dart';

const _duration = Duration(milliseconds: 300);
EventTransformer<Event> debounce<Event>(Duration duration) {
  return (events, mapper) => events.debounce(duration).switchMap(mapper);
}

class ChatBloc extends Bloc<ChatEvent, ChatState> {
  final ChatRepository chatRepository;

  ChatBloc(this.chatRepository) : super(ChatState()) {
    on<ChatInitialEvent>(_onInitialize);
    on<SendMessageEvent>(_onsendMessageSocketEvent);
    on<GetChatListEvent>(_ongetChatListEvent);
    on<GetChatMessageListEvent>(_getchatMessageListApiEvent);
    on<UpdateChatMessageSocketEvent>(_onUpdateMessageListApiEvent);
    on<UpdateChatMessageListDirectEvent>(_onUpdateMessageListDirectEvent);
    on<TypingSocketEvent>(_onTypingSocketEvent);
    on<SearchUserListEvent>(
      _onUserSearchEvent,
      transformer: debounce(_duration),
    );
    // on<ReadMessageEvent>(_onreadMessageSocketEvent);
    on<ChatListMessageEvent>(_onchatListMessageEvent);
    on<DeleteChatApiEvent>(_onDeleteChatApi);
    on<RefreshChatGetApiEvent>(_onRefreshChatGetApi);
    on<ClearChatTextFieldEvent>(_onClearChatTextField);
  }
  _onInitialize(ChatInitialEvent event, Emitter<ChatState> emit) {
    emit(
      state.copyWith(
        allFetch: false,
        chatList: [],
        chatMessageList: [],
        searchuserList: [],
        hasMore: true,
        isLoadingMore: false,
        isloding: true,
        scrollController: ScrollController(),
        searchController: TextEditingController(),
        chatController: TextEditingController(),
      ),
    );
  }

  _onsendMessageSocketEvent(
    SendMessageEvent event,
    Emitter<ChatState> emit,
  ) async {
    try {
      // Add message immediately to UI for better user experience (optimistic update)
      final currentUserId = Prefobj.preferences?.get(Prefkeys.USER_ID) as int?;
      if (currentUserId != null) {
        final tempMessage = ChatMessageData(
          id: DateTime.now().millisecondsSinceEpoch, // Temporary ID
          message: event.message,
          sentBy: currentUserId,
          type: event.type,
          createdAt: DateTime.now().toIso8601String(),
          isPending: true, // Mark as pending until server confirms
        );

        // Create a new list with the message added at the beginning
        final updatedMessageList = List<ChatMessageData>.from(
          state.chatMessageList,
        );
        updatedMessageList.insert(0, tempMessage);

        emit(state.copyWith(chatMessageList: updatedMessageList));
        Logger.lOG("Added message optimistically to UI: ${event.message}");
      }

      // Send the message via socket
      final socketData = {
        'Authorization': Prefobj.preferences?.get(Prefkeys.AUTHTOKEN),
        'message': event.message,
        'to': event.touserId,
        'type': event.type,
        'file': event.file,
      };

      Logger.lOG("Sending message via socket:");
      Logger.lOG("Event: ${SocketConfig.sendmessage}");
      Logger.lOG("Data: $socketData");

      SocketService.emit(SocketConfig.sendmessage, socketData);

      Logger.lOG("Message sent via socket: ${event.message}");

      // Set a timeout to mark message as failed if no response after 30 seconds
      Timer(const Duration(seconds: 30), () {
        final messageStillPending = state.chatMessageList.any(
          (msg) =>
              msg.message == event.message &&
              msg.sentBy == currentUserId &&
              msg.isPending == true,
        );

        if (messageStillPending) {
          Logger.lOG("Message timeout - marking as failed: ${event.message}");
          // You could add a failed state here if needed
          // For now, we'll just keep it as pending
        }
      });
    } catch (e) {
      Logger.lOG(e.toString());
    }
  }

  // _ongetChatListEvent(GetChatListEvent event, Emitter<ChatState> emit) async {
  //   try {
  //     emit(state.copyWith(isloding: true));
  //     final result = await apiClient.getChatListApi(event.page);
  //     if (result.results?.status == true) {
  //       state.chatList.addAll(result.results?.data ?? []);
  //       Logger.lOG("TOTAL CHAT USER LENGTH : ${state.chatList.length}");

  //       emit(state.copyWith(chatList: state.chatList, page: event.page, allFetch: false, isloding: false, chatListModel: result, isLoadingMore: false));
  //     }
  //   } catch (e) {
  //     await Future.delayed(const Duration(seconds: 1), () {
  //       emit(state.copyWith(isLoadingMore: false, allFetch: true, isloding: false));
  //     });
  //     await Future.delayed(const Duration(seconds: 1), () {
  //       emit(state.copyWith(allFetch: false, isloding: false));
  //     });
  //   }
  // }

  _ongetChatListEvent(GetChatListEvent event, Emitter<ChatState> emit) async {
    try {
      if (event.page == 1) {
        emit(state.copyWith(isloding: true));
      } else {
        emit(state.copyWith(isLoadingMore: true));
      }
      // final result = await apiClient.getChatListApi(event.page);

      // if (result.results?.status == true) {
      //   state.chatList.clear();
      //   final newData = result.results?.data ?? [];

      //   // Filter duplicate data based on unique ID
      //   final uniqueData = newData.where((newItem) => !state.chatList.any((existingItem) => existingItem.userId == newItem.userId)).toList();

      //   state.chatList.addAll(uniqueData);
      //   Logger.lOG("TOTAL CHAT USER LENGTH : ${state.chatList.length}");

      //   emit(state.copyWith(
      //     chatList: List.from(state.chatList), // Ensure state is updated with a new list
      //     page: event.page,
      //     allFetch: false,
      //     isloding: false,
      //     chatListModel: result,
      //     isLoadingMore: false,
      //   ));
      // }
    } catch (e) {
      await Future.delayed(const Duration(seconds: 1), () {
        emit(
          state.copyWith(isLoadingMore: false, allFetch: true, isloding: false),
        );
      });
      await Future.delayed(const Duration(seconds: 1), () {
        emit(state.copyWith(allFetch: false, isloding: false));
      });
    }
  }

  _getchatMessageListApiEvent(
    GetChatMessageListEvent event,
    Emitter<ChatState> emit,
  ) async {
    Logger.lOG(
      "Getting chat messages for userId: ${event.userId}, page: ${event.page}",
    );

    if (event.page == 1) {
      emit(state.copyWith(isloding: true, chatMessageList: []));
    } else {
      emit(state.copyWith(isLoadingMore: true));
    }

    try {
      final result = await chatRepository.getChatMessageListApi(
        event.userId,
        event.page,
      );

      try {
        Logger.lOG("API Response: ${result.toJson()}");
      } catch (e) {
        Logger.lOG("API Response received but couldn't serialize to JSON: $e");
        Logger.lOG("Response status: ${result.results?.status}");
        Logger.lOG("Response message: ${result.results?.message}");
        Logger.lOG("Response data count: ${result.results?.data?.length}");
      }

      if (result.results?.status == true) {
        final newMessages = result.results?.data ?? [];
        Logger.lOG("Received ${newMessages.length} new messages");

        List<ChatMessageData> updatedMessageList;

        if (event.page == 1) {
          // For first page, replace the entire list to avoid duplicates
          updatedMessageList = List<ChatMessageData>.from(newMessages);
        } else {
          // For subsequent pages, add only new messages that don't already exist
          updatedMessageList = List<ChatMessageData>.from(
            state.chatMessageList,
          );

          for (final newMessage in newMessages) {
            final messageExists = updatedMessageList.any(
              (existingMessage) => existingMessage.id == newMessage.id,
            );
            if (!messageExists) {
              updatedMessageList.add(newMessage);
            }
          }
        }

        Logger.lOG("TOTAL CHAT List LENGTH : ${updatedMessageList.length}");

        emit(
          state.copyWith(
            chatMessageList: updatedMessageList,
            page: event.page,
            allFetch: false,
            isloding: false,
            chatMessageListModel: result,
            isLoadingMore: false,
          ),
        );
      } else {
        Logger.lOG("API returned status false: ${result.results?.message}");
        emit(state.copyWith(isloding: false, isLoadingMore: false));
      }
    } catch (e, stackTrace) {
      Logger.lOG("Error in _getchatMessageListApiEvent: $e");
      Logger.lOG("Stack trace: $stackTrace");

      // Emit error state
      emit(state.copyWith(isloding: false, isLoadingMore: false));

      await Future.delayed(const Duration(seconds: 1), () {
        emit(
          state.copyWith(isLoadingMore: false, allFetch: true, isloding: false),
        );
      });
      await Future.delayed(const Duration(seconds: 1), () {
        emit(state.copyWith(allFetch: false, isloding: false));
      });
    }
  }

  _onUpdateMessageListApiEvent(
    UpdateChatMessageSocketEvent event,
    Emitter<ChatState> emit,
  ) {
    try {
      Logger.lOG("Processing socket message: ${event.message}");
      Logger.lOG("Message ID: ${event.id}, Sent by: ${event.sentby}");

      final currentUserId = Prefobj.preferences?.get(Prefkeys.USER_ID) as int?;
      final updatedMessageList = List<ChatMessageData>.from(
        state.chatMessageList,
      );

      // Check if message already exists by ID (avoid duplicates)
      final messageExists = updatedMessageList.any((msg) => msg.id == event.id);

      if (messageExists) {
        Logger.lOG(
          "Message with ID ${event.id} already exists, skipping duplicate",
        );
        return;
      }

      // For sent messages, try to replace the optimistic (pending) message
      if (event.sentby == currentUserId) {
        // Find the pending message with the same content
        final tempMessageIndex = updatedMessageList.indexWhere(
          (msg) =>
              msg.message == event.message &&
              msg.sentBy == currentUserId &&
              msg.isPending == true,
        );

        if (tempMessageIndex != -1) {
          // Replace the temporary message with the real one from server
          updatedMessageList[tempMessageIndex] = ChatMessageData(
            id: event.id,
            createdAt: event.createdat,
            message: event.message,
            sentBy: event.sentby,
            type: event.type,
            isPending: false, // Server confirmed message
          );
          Logger.lOG("Replaced optimistic message with server response");
        } else {
          // Add new message if no pending message found
          updatedMessageList.insert(
            0,
            ChatMessageData(
              id: event.id,
              createdAt: event.createdat,
              message: event.message,
              sentBy: event.sentby,
              type: event.type,
              isPending: false,
            ),
          );
          Logger.lOG("Added new sent message from socket");
        }
      } else {
        // For received messages, just add to the beginning
        updatedMessageList.insert(
          0,
          ChatMessageData(
            id: event.id,
            createdAt: event.createdat,
            message: event.message,
            sentBy: event.sentby,
            type: event.type,
            isPending: false,
          ),
        );
        Logger.lOG("Added new received message from socket");
      }

      emit(state.copyWith(chatMessageList: updatedMessageList));
    } catch (e) {
      Logger.lOG("Error in _onUpdateMessageListApiEvent: $e");
    }
  }
  // _onUpdateMessageListApiEvent(
  //   UpdateChatMessageSocketEvent event,
  //   Emitter<ChatState> emit,
  // ) {
  //   try {
  //     Logger.lOG("Adding new message to chat: ${event.message}");
  //     Logger.lOG("Message sent by: ${event.sentby}");

  //     final currentUserId = Prefobj.preferences?.get(Prefkeys.USER_ID) as int?;
  //     final updatedMessageList = List<ChatMessageData>.from(
  //       state.chatMessageList,
  //     );

  //     // Check for duplicates - prioritize ID check first
  //     final messageExists = updatedMessageList.any((msg) => msg.id == event.id);

  //     if (messageExists) {
  //       Logger.lOG(
  //         "Message with ID ${event.id} already exists, skipping duplicate",
  //       );
  //       return;
  //     }

  //     // Check if this is a response to an optimistically added message
  //     // Look for a temporary message with similar content from the same user
  //     final tempMessageIndex = updatedMessageList.indexWhere(
  //       (msg) =>
  //           msg.message == event.message &&
  //           msg.sentBy == event.sentby &&
  //           msg.type == event.type &&
  //           msg.sentBy ==
  //               currentUserId && // Only replace our own optimistic messages
  //           _isWithinTimeWindow(msg.createdAt, event.createdat),
  //     );

  //     final newMessage = ChatMessageData(
  //       id: event.id,
  //       createdAt: event.createdat,
  //       message: event.message,
  //       sentBy: event.sentby,
  //       type: event.type,
  //       isPending: false, // Server confirmed message
  //     );

  //     if (tempMessageIndex != -1) {
  //       // Replace the temporary message with the real one from server
  //       updatedMessageList[tempMessageIndex] = newMessage;
  //       Logger.lOG("Replaced optimistic message with server response");
  //     } else {
  //       // Add new message (this is a message from another user or a new message)
  //       updatedMessageList.insert(0, newMessage);
  //       Logger.lOG("Added new message from socket");
  //     }

  //     Logger.lOG("Updated chat list length: ${updatedMessageList.length}");
  //     emit(state.copyWith(chatMessageList: updatedMessageList));
  //   } catch (e) {
  //     Logger.lOG("Error in _onUpdateMessageListApiEvent: ${e.toString()}");
  //   }
  // }

  _onUpdateMessageListDirectEvent(
    UpdateChatMessageListDirectEvent event,
    Emitter<ChatState> emit,
  ) {
    try {
      Logger.lOG(
        "Updating message list directly with ${event.messageList.length} messages",
      );
      emit(state.copyWith(chatMessageList: event.messageList));
    } catch (e) {
      Logger.lOG("Error in _onUpdateMessageListDirectEvent: ${e.toString()}");
    }
  }

  _onTypingSocketEvent(TypingSocketEvent event, Emitter<ChatState> emit) {
    try {
      SocketService.emit(SocketConfig.isTyping, {
        'Authorization': Prefobj.preferences?.get(Prefkeys.AUTHTOKEN),
        'to': event.userId,
        'is_typing': event.isTyping,
      });
    } catch (e) {
      Logger.lOG(e.toString());
    }
  }

  _onUserSearchEvent(SearchUserListEvent event, Emitter<ChatState> emit) async {
    emit(state.copyWith(searchuserListLoading: true));
    try {
      final completer = Completer<Map<String, dynamic>>();
      SocketService.emit(SocketConfig.searchuser, {
        'Authorization': Prefobj.preferences?.get(Prefkeys.AUTHTOKEN),
        'search_text': event.searchtext,
      });
      bool isCompleted = false;

      SocketService.response(SocketConfig.searchuser, (response) {
        if (!isCompleted) {
          completer.complete(response);
          isCompleted = true;
          emit(state.copyWith(searchuserListLoading: true));
        }
      });

      final response = await completer.future;
      Future.delayed(Duration(milliseconds: 550), () {
        if (response['data'] == null || response['data'].isEmpty) {
          emit(
            state.copyWith(searchuserList: [], searchuserListLoading: false),
          );
        }
      });

      final data = response['data'] as List<dynamic>;
      List<SearchUserData> allUsers = data.map((item) {
        if (item is Map<String, dynamic>) {
          return SearchUserData(
            userId: item['id'],
            name: item['name'],
            userName: item['username'],
            profileImage: item['profile'],
          );
        }
        throw Exception('Invalid data format');
      }).toList();
      final searchText = event.searchtext.toLowerCase();

      if (searchText.isEmpty) {
        emit(state.copyWith(searchuserListLoading: false));
        emit(state.copyWith(chatList: state.chatList));
      } else {
        final filteredUsers = allUsers.where((user) {
          return user.userName?.toLowerCase().contains(searchText) ?? false;
        }).toList();
        Logger.lOG(filteredUsers);
        emit(
          state.copyWith(
            searchuserList: filteredUsers,
            searchuserListLoading: false,
          ),
        );
      }
    } catch (e) {
      emit(state.copyWith(searchuserListLoading: false));
      Logger.lOG(e.toString());
    }
  }

  // _onreadMessageSocketEvent(ReadMessageEvent event, Emitter<ChatState> emit) async {
  //   try {
  //     SocketService.emit(SocketConfig.messageRead, {
  //       'Authorization': Prefobj.preferences?.get(Prefkeys.AUTHTOKEN),
  //       'to': event.touserId,
  //       'message_id': event.messageId,
  //     });
  //   } catch (e) {
  //     Logger.lOG(e.toString());
  //   }
  // }

  _onchatListMessageEvent(
    ChatListMessageEvent event,
    Emitter<ChatState> emit,
  ) async {
    final completer = Completer<Map<String, dynamic>>();
    SocketService.emit(SocketConfig.chatList, {
      'Authorization': Prefobj.preferences?.get(Prefkeys.AUTHTOKEN),
      'page': event.page,
    });
    bool isCompleted = false;
    SocketService.response(SocketConfig.chatList, (response) {
      if (!isCompleted) {
        completer.complete(response);
        isCompleted = true;
      }
    });

    // Await the response
    final response = await completer.future;
    if (response['data']['results']['data'] == null) {
      emit(state.copyWith(chatList: []));
    } else {
      emit(state.copyWith(chatList: response['data']['results']['data']));
    }
  }

  _onDeleteChatApi(DeleteChatApiEvent event, Emitter<ChatState> emit) async {
    try {
      //   final result = await apiClient.deleteChatApi(event.userId.toString());
      //   if (result.status == true) {
      //     await _onRefreshChatGetApi(RefreshChatGetApiEvent(userId: event.userId.toString()), emit);
      //   }
    } catch (e) {
      Logger.lOG(e.toString());
    }
  }

  _onRefreshChatGetApi(
    RefreshChatGetApiEvent event,
    Emitter<ChatState> emit,
  ) async {
    try {
      // final result = await apiClient.deleteChatApi(
      //   event.userId,
      // );
      // if (result.status == true) {
      //   NavigatorService.goBack();

      //   emit(state.copyWith(deletechatmodel: result));
      // }
    } catch (e) {
      Logger.lOG(e.toString());
    }
  }

  _onClearChatTextField(
    ClearChatTextFieldEvent event,
    Emitter<ChatState> emit,
  ) {
    // Clear the chat controller text
    if (state.chatController != null) {
      Logger.lOG(
        "Clearing chat controller text. Current text: '${state.chatController!.text}'",
      );
      state.chatController!.clear();
      Logger.lOG(
        "Chat controller cleared. New text: '${state.chatController!.text}'",
      );

      // Create a new TextEditingController to force UI rebuild
      final newController = TextEditingController();

      // Emit a new state with the new controller to trigger UI rebuild
      emit(state.copyWith(chatController: newController));
    } else {
      Logger.lOG("Chat controller is null, cannot clear");
    }
  }
}
