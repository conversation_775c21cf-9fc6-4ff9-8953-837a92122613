import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:room_eight/views/chat/bloc/chat_bloc.dart';

void main() {
  group('Chat Text Field Clearing Tests', () {
    test('should create ClearChatTextFieldEvent correctly', () {
      // Arrange & Act
      const event = ClearChatTextFieldEvent();

      // Assert
      expect(event, isA<ClearChatTextFieldEvent>());
      expect(event.props, isEmpty);
    });

    test('should verify TextEditingController clear functionality', () {
      // Arrange
      final controller = TextEditingController();
      controller.text = 'Test message';

      // Verify initial state
      expect(controller.text, equals('Test message'));

      // Act - simulate what the BLoC does
      controller.clear();

      // Assert
      expect(controller.text, equals(''));
    });

    test('should verify new controller creation clears text', () {
      // Arrange
      final oldController = TextEditingController();
      oldController.text = 'Test message';

      // Verify initial state
      expect(oldController.text, equals('Test message'));

      // Act - simulate what the updated BLoC does
      final newController = TextEditingController();

      // Assert
      expect(newController.text, equals(''));
      expect(
        oldController.text,
        equals('Test message'),
      ); // Old controller unchanged
    });
  });
}
